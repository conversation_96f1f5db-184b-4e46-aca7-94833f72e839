# 游戏战术室

这是一个游戏战术室（微信小程序）的后端项目，支持多种游戏，玩家可以在这个系统上制定战术。

## 系统信息

1. 实体的主键 id 使用雪花算法生成、所有 Long 字段返回前端的时候都转为 String，使用 jackson 自定义序列化实现
2. 使用 swagger 展示接口信息，swagger 的接口信息中，Long 要使用 String 代替

> 目前支持的游戏有：三国·冰河时代

## 三国·冰河时代

在这个游戏中，有多个区，每个区有独一无二的数字区号，从 1 开始，每个小程序用户可以在每个区中创建两个账号，可以在每个区中创建多个联盟，每个账号只能加入一个联盟，每个联盟的正式成员总数不能超过 100 人。

1. 在联盟中，联盟包含联盟所在的区号（从 1 开始的整数），联盟名称，联盟成员有价位区分，分为一阶至五阶
2. 每个账号有账号所在的区号，账号名称、战力值、伤害加成（保留两位小数的数字，比如 12.14、16.35 这类数字）、兵等级（整数，1-30）、兵量，集结容量（出征时兵的集结总数，以万为单位，可选），吕布星级
3. 联盟有战事安排，现在有四种战事，分别为：官渡一、官渡二、攻城、守城，联盟成员只能申请加入官渡一或者官渡二，不能同时申请官渡一和官渡二，攻城和守城由盟主安排，不需要成员申请

现在需要以下功能：

1. 微信小程序用户登录、查询小程序用户信息
2. 除登录接口以外的其他接口使用 JWT 认证
3. 用户创建联盟（生成全局唯一的六位由英文数字组合的联盟编码）、更新（更新联盟名称、联盟编码）、删除联盟、转交联盟（将盟主转交到其他小程序用户）
4. 用户创建、更新、删除游戏账号
5. 小程序用户能查询自己创建的所有联盟和所有账号（接口的返回数据中使用两个字段展示，一个字段展示创建的所有联盟集合，一个字段展示创建的所有账号集合，根据区号进行降序排序）
6. 账号申请加入联盟（通过联盟编码加入申请），创建的某个区的游戏账号只能申请加入同一个区的联盟
7. 盟主可以通过或者拒绝账号加入联盟的申请，如果通过申请，则账号成为联盟的正式成员之一
8. 盟主可以移除成员
9. 小程序用户更新账号信息（不能更新账号所在的区号）、盟主能更新盟里成员的账号信息
10. 成员可以申请加入官渡一或者官渡二战事
11. 盟主可以通过或者拒绝成员加入官渡一或者官渡二的申请
12. 盟主可以将成员的移动到另一个官渡战事，比如成员申请的官渡一，盟主可以将成员改为申请官渡二
13. 每一种战事下，盟主可以安排相关成员，战事下有战事分组，盟主可以安排成员进入战事分组，也可以不安排进分组，同时，盟主可以创建、更新、删除战事分组，分组包含分组名称、分组任务、分组成员
14. 盟主可以将成员添加到战事分组，也可以在同一种战事下将一个分组下的某个成员移动到另一个分组，也就是在同一种战事下，成员可以安排进分组，也可以不安排进分组，没有移动到分组的成员属于战事机动人员
15. 盟主可以清空战事中的所有成员安排
16. 可以查询每一种战事的人员安排
17. 小程序用户可以查询账号申请加入联盟的状态、可以查询账号申请加入官渡一或者官渡二的状态
18. 可以查询联盟的详细信息，包含联盟基本信息和所有战事的人员安排
19. 盟主可以查询申请加入联盟的账号列表、查询申请加入官渡一和官渡二的申请列表
20. 每个用户在每个区都能且只能创建一个王朝，创建王朝的时候生成王朝编码，创建王朝的用户就是这个王朝的天子
21. 每个账号都只能加入同区的一个王朝（使用王朝编码加入），加入后就是这个王朝的成员，只需要有王朝编码就能加入王朝
22. 每个王朝都有“太尉”和“尚书令”两种官职，每一种官职都有 24 个时间段（00:00:00~00:59:59、01:00:00~01:59:59 等），每个时段只能有一个王朝成员任职，需要王朝成员抢夺，如果某个时段被某个王朝成员抢了，那么其他成员无法抢夺（需要考虑多个成员在同一时间抢夺同一个时段的官职的情况）
23. 天子可以设置每种官职可以开始抢夺的开始时间和结束的时间（年月日小时分钟秒），抢夺的官职的任职日期（年月日），抢夺的时间必须早于任职日期，比如设置的开始抢夺时间是：2025 年 8 月 11 日 10:00:00 到 2025 年 8 月 11 日 23:00:00（结束时间必须跟开始时间处于同一天），抢夺的官职的任职日期必须在 2025 年 8 月 11 日之后，比如 2025 年 8 月 12 日，那么成员就可以在 2025 年 8 月 11 日 10:00:00 到 2025 年 8 月 11 日 23:00:00 之间抢夺 2025 年 8 月 12 日每种官职的 24 个时段的任意一个时段
24. 天子可以设置官职的哪些时段无法被抢夺，可以关闭或开启所有官职的抢夺，可以清空抢夺的结果，同时，系统不需要保留历史抢夺的结果数据，在天子设置每一种官职抢夺的开始时间和抢夺的任职时间之后清空王朝该官职的抢夺结果的历史数据
25. 天子和王朝成员都可以查看所有官职的抢夺结果
26. 微信限制每个用户每次点击订阅按钮，最多只能订阅一条模板消息，点击两次按钮则能接收两条模板消息，所以用户要订阅多少条消息则需要点击多少次，系统需要统计用户订阅了多少条消息，每次发送一条消息给用户可接收的消息数量就减 1，并且把用户可接收剩余多少条消息的信息在微信消息中展示出来。
27. 每个账号可以设置是否接收加入的联盟的预定活动时间的微信消息，消息的类型包括：三英战吕布、官渡报名、煮酒论英雄、南蛮入侵、攻城、守城、刷功勋，每个账号可以设置接收哪些类型的联盟消息，联盟盟主可以发送微信消息给用户，以下为消息模板，其中发起方的格式为：{联盟名称}（{区服}），活动名称就是活动类型，开始时间由盟主设置，备注的内容也由盟主设置，备注的内容默认为：活动时间为活动预计开启时间，如有变更，盟主将另行通知（预计剩余可接收{num}条通知）。
    模板 ID i4cXxSfmC7eSUaZxRL4u8sT_eBPCQ5RV_xmW0tmV85c
    模板编号 432
    发起方 {{thing1.DATA}}
    活动名称 {{thing6.DATA}}
    开始时间 {{date2.DATA}}
    备注 {{thing11.DATA}}
28. 在清空官渡一和官渡二的战事安排的时候可以选择是否发送官渡报名通知，默认不发送消息
29. 用户可以设置是否接收三国·冰河时代游戏的参加演武场的微信通知，如果用户设置了接收通知，那么在每天晚上的 22:53 分给用户发送微信通知，提醒用户参加演武场，使用第 27 点的模板发送微信消息
30. 联盟管理员可以设置加入联盟、申请参加官渡（包括官渡一和官渡二）是否需要审核，如果设置的不需要审核，那么在申请加入联盟的时候就直接是联盟成员，申请官渡的时候就直接是官渡的成员
31. 游戏中有一个活动叫南蛮入侵，在每个联盟里面，每个联盟成员都可以创建南蛮分组，加入南蛮分组，分组包括分组名称和队列数量（数量为：1 到 6），每个成员只能加入一个分组，当成员加入另一个分组的时候，如果成员原来所在的分组中没有其他成员了，则直接删除原来的分组（如果联盟成员退出联盟或者被移出联盟，如果所在的南蛮分组没有其他成员也自动删除分组），支持根据队列数量查询联盟中的南蛮分组，支持查看分组成员信息，支持查询账号所在的南蛮分组。
32. 联盟管理员支持设置官渡一和官渡二的人数上限，默认官渡一 40 人、官渡二 40 人，人数包含已在战事中的人数和申请人数，两者相加达到上限后，则账号无法再继续申请加入对应的战事。
