package com.app.gamehub.repository;

import com.app.gamehub.entity.WarApplication;
import com.app.gamehub.entity.WarType;
import java.util.Collection;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface WarApplicationRepository extends JpaRepository<WarApplication, Long> {

  List<WarApplication> findByAllianceIdAndWarTypeAndStatusOrderByCreatedAtAsc(
      Long allianceId, WarType warType, WarApplication.ApplicationStatus status);

  List<WarApplication> findByAccountIdOrderByCreatedAtDesc(Long accountId);

  boolean existsByAccountIdAndWarTypeIn(Long accountId, Collection<WarType> warTypes);

  void deleteAllByAccountId(Long id);

  void deleteAllByAllianceId(Long id);

  void deleteByAccountIdAndWarType(Long accountId, WarType warType);

  long countByAllianceIdAndWarTypeAndStatus(Long allianceId, WarType warType, WarApplication.ApplicationStatus status);
}
