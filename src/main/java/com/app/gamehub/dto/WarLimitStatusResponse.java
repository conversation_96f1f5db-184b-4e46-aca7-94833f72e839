package com.app.gamehub.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "官渡战事人数上限状态响应")
public class WarLimitStatusResponse {

  @Schema(description = "官渡一人数上限")
  private Integer guanduOneLimit;

  @Schema(description = "官渡一当前已安排人数")
  private Long guanduOneArranged;

  @Schema(description = "官渡一当前待处理申请人数")
  private Long guanduOnePending;

  @Schema(description = "官渡一总人数")
  private Long guanduOneTotal;

  @Schema(description = "官渡一是否已满")
  private Boolean guanduOneFull;

  @Schema(description = "官渡二人数上限")
  private Integer guanduTwoLimit;

  @Schema(description = "官渡二当前已安排人数")
  private Long guanduTwoArranged;

  @Schema(description = "官渡二当前待处理申请人数")
  private Long guanduTwoPending;

  @Schema(description = "官渡二总人数")
  private Long guanduTwoTotal;

  @Schema(description = "官渡二是否已满")
  private Boolean guanduTwoFull;
}
