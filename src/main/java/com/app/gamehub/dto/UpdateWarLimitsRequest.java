package com.app.gamehub.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "更新官渡战事人数上限请求")
public class UpdateWarLimitsRequest {

  @NotNull(message = "联盟ID不能为空")
  @Schema(description = "联盟ID", example = "1")
  private Long allianceId;

  @Schema(description = "官渡一人数上限", example = "40")
  @Min(value = 1, message = "官渡一人数上限最小为1")
  @Max(value = 200, message = "官渡一人数上限最大为200")
  private Integer guanduOneLimit;

  @Schema(description = "官渡二人数上限", example = "40")
  @Min(value = 1, message = "官渡二人数上限最小为1")
  @Max(value = 200, message = "官渡二人数上限最大为200")
  private Integer guanduTwoLimit;
}
