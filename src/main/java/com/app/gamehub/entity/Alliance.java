package com.app.gamehub.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "alliances")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Alliance extends BaseEntity {

  @Column(name = "name", nullable = false)
  private String name;

  @Column(name = "code", unique = true, nullable = false, length = 6)
  private String code;

  @Column(name = "server_id", nullable = false)
  private Integer serverId;

  @Column(name = "leader_id", nullable = false)
  private Long leaderId;

  @Column(name = "alliance_join_approval_required", nullable = false)
  private Boolean allianceJoinApprovalRequired = true;

  @Column(name = "war_join_approval_required", nullable = false)
  private Boolean warJoinApprovalRequired = true;

  @Column(name = "guandu_one_limit", nullable = false)
  private Integer guanduOneLimit = 40;

  @Column(name = "guandu_two_limit", nullable = false)
  private Integer guanduTwoLimit = 40;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "leader_id", insertable = false, updatable = false)
  private User leader;
}
