package com.app.gamehub.entity;

import com.app.gamehub.enums.ActivityType;
import com.app.gamehub.validation.ValidLvbuStarLevel;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "game_accounts")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class GameAccount extends BaseEntity {

  @Column(name = "user_id", nullable = false)
  private Long userId;

  @Column(name = "server_id", nullable = false)
  private Integer serverId;

  @Column(name = "account_name", nullable = false)
  private String accountName;

  @Column(name = "power_value", nullable = false)
  private Long powerValue;

  @Column(name = "damage_bonus", precision = 5, scale = 2)
  private BigDecimal damageBonus;

  @Column(name = "troop_level")
  private Integer troopLevel;

  @Column(name = "rally_capacity")
  private Integer rallyCapacity;

  @NotNull(message = "吕布星级不能为空")
  @ValidLvbuStarLevel
  @Column(name = "lvbu_star_level", nullable = false, precision = 2, scale = 1)
  private BigDecimal lvbuStarLevel = BigDecimal.ZERO;

  @Column(name = "alliance_id")
  private Long allianceId;

  @Column(name = "dynasty_id")
  private Long dynastyId;

  @Column(name = "barbarian_group_id")
  private Long barbarianGroupId;

  @Column(name = "member_tier")
  @Enumerated(EnumType.STRING)
  private MemberTier memberTier;

  @ElementCollection(targetClass = ActivityType.class)
  @CollectionTable(name = "game_account_notification_types",
                   joinColumns = @JoinColumn(name = "account_id"))
  @Enumerated(EnumType.STRING)
  @Column(name = "activity_type")
  private Set<ActivityType> notificationTypes = Set.of(
      ActivityType.SAN_YING_ZHAN_LV_BU,
      ActivityType.GUAN_DU_BAO_MING,
      ActivityType.ZHU_JIU_LUN_YING_XIONG,
      ActivityType.NAN_MAN_RU_QIN,
      ActivityType.GONG_CHENG,
      ActivityType.SHOU_CHENG,
      ActivityType.SHUA_GONG_XUN
  );

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id", insertable = false, updatable = false)
  private User user;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "alliance_id", insertable = false, updatable = false)
  private Alliance alliance;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "dynasty_id", insertable = false, updatable = false)
  private Dynasty dynasty;

  public enum MemberTier {
    TIER_1,
    TIER_2,
    TIER_3,
    TIER_4,
    TIER_5
  }
}
