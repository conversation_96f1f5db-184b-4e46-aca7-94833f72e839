package com.app.gamehub.service;

import com.app.gamehub.dto.UseTacticRequest;
import com.app.gamehub.dto.WarLimitStatusResponse;
import com.app.gamehub.dto.WarRequest;
import com.app.gamehub.entity.*;
import com.app.gamehub.exception.BusinessException;
import com.app.gamehub.model.TacticalArrangement;
import com.app.gamehub.model.WarTactic;
import com.app.gamehub.repository.*;
import com.app.gamehub.util.UserContext;
import jakarta.validation.Valid;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class WarService {

  private final WarApplicationRepository warApplicationRepository;
  private final GameAccountRepository gameAccountRepository;
  private final AllianceRepository allianceRepository;
  private final WarArrangementRepository warArrangementRepository;
  private final WarGroupRepository warGroupRepository;

  @Transactional
  public WarApplication applyForWar(WarRequest request) {
    Long userId = UserContext.getUserId();
    Long accountId = request.getAccountId();

    // 验证账号是否存在且属于当前用户
    GameAccount account =
        gameAccountRepository
            .findById(accountId)
            .orElseThrow(() -> new BusinessException("游戏账号不存在"));

    if (!account.getUserId().equals(userId)) {
      throw new BusinessException("只能为自己的账号申请参加战事");
    }

    // 验证账号是否已加入联盟
    if (account.getAllianceId() == null) {
      throw new BusinessException("必须先加入联盟才能申请参加战事");
    }

    // 验证战事类型（只能申请官渡一或官渡二）
    if (request.getWarType() != WarType.GUANDU_ONE && request.getWarType() != WarType.GUANDU_TWO) {
      throw new BusinessException("只能申请参加官渡一或官渡二战事");
    }

    // 检查是否已有官渡战事的待处理申请
    List<WarType> guanduWars = Arrays.asList(WarType.GUANDU_ONE, WarType.GUANDU_TWO);
    if (warApplicationRepository.existsByAccountIdAndWarTypeIn(accountId, guanduWars)) {
      throw new BusinessException("只能参与一场官渡战事");
    }

    // 获取联盟信息以检查审核设置
    Alliance alliance = allianceRepository.findById(account.getAllianceId())
        .orElseThrow(() -> new BusinessException("联盟不存在"));

    // 检查官渡战事人数上限
    if (WarType.isGuanDu(request.getWarType())) {
      checkWarLimit(alliance, request.getWarType());
    }

    // 创建申请
    WarApplication application = new WarApplication();
    application.setAccountId(accountId);
    application.setAllianceId(account.getAllianceId());
    application.setWarType(request.getWarType());

    // 检查是否需要审核
    if (alliance.getWarJoinApprovalRequired()) {
      // 需要审核，设置为待处理状态
      application.setStatus(WarApplication.ApplicationStatus.PENDING);
      log.info("账号 {} 申请参加战事 {}，等待审核", accountId, request.getWarType());
    } else {
      // 不需要审核，直接通过
      application.setStatus(WarApplication.ApplicationStatus.APPROVED);
      application.setProcessedBy(alliance.getLeaderId()); // 系统自动处理，记录为盟主处理

      // 直接加入战事安排
      WarArrangement arrangement = new WarArrangement();
      arrangement.setAccountId(accountId);
      arrangement.setAllianceId(account.getAllianceId());
      arrangement.setWarType(request.getWarType());
      // warGroupId 为 null，表示机动人员
      warArrangementRepository.save(arrangement);

      log.info("账号 {} 自动加入战事 {}（无需审核）", accountId, request.getWarType());
    }

    return warApplicationRepository.save(application);
  }

  @Transactional
  public WarApplication processWarApplication(Long applicationId, boolean approved) {
    Long userId = UserContext.getUserId();
    WarApplication application =
        warApplicationRepository
            .findById(applicationId)
            .orElseThrow(() -> new BusinessException("申请不存在"));

    // 验证是否为盟主
    Alliance alliance =
        allianceRepository
            .findById(application.getAllianceId())
            .orElseThrow(() -> new BusinessException("联盟不存在"));

    if (!alliance.getLeaderId().equals(userId)) {
      throw new BusinessException("只有盟主可以处理战事申请");
    }

    // 验证申请状态
    if (application.getStatus() != WarApplication.ApplicationStatus.PENDING) {
      throw new BusinessException("申请已被处理");
    }

    if (approved) {
      if (WarType.isGuanDu(application.getWarType())) {
        if (warArrangementRepository.existsByAccountIdAndWarTypeIn(
            application.getAccountId(), WarType.allGuanDu())) {
          throw new BusinessException("一个账号只能参与一场官渡战事");
        }

        // 检查人数上限（审核通过时需要重新检查，因为可能有其他申请已经通过）
        checkWarLimitForApproval(alliance, application.getWarType());
      }
      application.setStatus(WarApplication.ApplicationStatus.APPROVED);
      WarArrangement arrangement = new WarArrangement();
      arrangement.setAccountId(application.getAccountId());
      arrangement.setAllianceId(alliance.getId());
      arrangement.setWarType(application.getWarType());
      warArrangementRepository.save(arrangement);
    } else {
      application.setStatus(WarApplication.ApplicationStatus.REJECTED);
    }

    application.setProcessedBy(userId);
    return warApplicationRepository.save(application);
  }

  public List<WarApplication> getPendingWarApplications(Long allianceId, WarType warType) {
    Long userId = UserContext.getUserId();
    // 验证是否为盟主
    Alliance alliance =
        allianceRepository.findById(allianceId).orElseThrow(() -> new BusinessException("联盟不存在"));

    if (!alliance.getLeaderId().equals(userId)) {
      throw new BusinessException("只有盟主可以查看战事申请列表");
    }

    return warApplicationRepository.findByAllianceIdAndWarTypeAndStatusOrderByCreatedAtAsc(
        allianceId, warType, WarApplication.ApplicationStatus.PENDING);
  }

  public List<WarApplication> getAccountWarApplications(Long accountId) {
    return warApplicationRepository.findByAccountIdOrderByCreatedAtDesc(accountId);
  }

  /**
   * 检查官渡战事人数上限
   */
  private void checkWarLimit(Alliance alliance, WarType warType) {
    int limit;
    if (warType == WarType.GUANDU_ONE) {
      limit = alliance.getGuanduOneLimit();
    } else if (warType == WarType.GUANDU_TWO) {
      limit = alliance.getGuanduTwoLimit();
    } else {
      return; // 非官渡战事不检查人数上限
    }

    // 计算当前人数：已在战事中的人数 + 待处理申请人数
    long currentArrangementCount = warArrangementRepository.countByAllianceIdAndWarType(
        alliance.getId(), warType);
    long pendingApplicationCount = warApplicationRepository.countByAllianceIdAndWarTypeAndStatus(
        alliance.getId(), warType, WarApplication.ApplicationStatus.PENDING);

    long totalCount = currentArrangementCount + pendingApplicationCount;

    if (totalCount >= limit) {
      String warTypeName = warType == WarType.GUANDU_ONE ? "官渡一" : "官渡二";
      throw new BusinessException(String.format("%s人数已达上限（%d人）", warTypeName, limit));
    }

    log.info("战事 {} 当前人数检查：已安排 {}, 待处理申请 {}, 总计 {}, 上限 {}",
        warType, currentArrangementCount, pendingApplicationCount, totalCount, limit);
  }

  /**
   * 检查官渡战事人数上限（用于审核通过时）
   */
  private void checkWarLimitForApproval(Alliance alliance, WarType warType) {
    int limit;
    if (warType == WarType.GUANDU_ONE) {
      limit = alliance.getGuanduOneLimit();
    } else if (warType == WarType.GUANDU_TWO) {
      limit = alliance.getGuanduTwoLimit();
    } else {
      return; // 非官渡战事不检查人数上限
    }

    // 计算当前人数：已在战事中的人数 + 待处理申请人数（不包括当前正在处理的申请）
    long currentArrangementCount = warArrangementRepository.countByAllianceIdAndWarType(
        alliance.getId(), warType);
    long pendingApplicationCount = warApplicationRepository.countByAllianceIdAndWarTypeAndStatus(
        alliance.getId(), warType, WarApplication.ApplicationStatus.PENDING);

    // 审核通过时，当前申请会从待处理变为已安排，所以总数实际上不变
    // 但我们需要检查当前的已安排人数是否已经达到上限
    if (currentArrangementCount >= limit) {
      String warTypeName = warType == WarType.GUANDU_ONE ? "官渡一" : "官渡二";
      throw new BusinessException(String.format("%s人数已达上限（%d人），无法通过申请", warTypeName, limit));
    }

    log.info("审核通过时战事 {} 人数检查：已安排 {}, 待处理申请 {}, 上限 {}",
        warType, currentArrangementCount, pendingApplicationCount, limit);
  }

  /**
   * 获取联盟官渡战事人数上限状态
   */
  public WarLimitStatusResponse getWarLimitStatus(Long allianceId) {
    Alliance alliance = allianceRepository.findById(allianceId)
        .orElseThrow(() -> new BusinessException("联盟不存在"));

    WarLimitStatusResponse response = new WarLimitStatusResponse();

    // 官渡一统计
    response.setGuanduOneLimit(alliance.getGuanduOneLimit());
    response.setGuanduOneArranged(warArrangementRepository.countByAllianceIdAndWarType(
        allianceId, WarType.GUANDU_ONE));
    response.setGuanduOnePending(warApplicationRepository.countByAllianceIdAndWarTypeAndStatus(
        allianceId, WarType.GUANDU_ONE, WarApplication.ApplicationStatus.PENDING));
    response.setGuanduOneTotal(response.getGuanduOneArranged() + response.getGuanduOnePending());
    response.setGuanduOneFull(response.getGuanduOneTotal() >= alliance.getGuanduOneLimit());

    // 官渡二统计
    response.setGuanduTwoLimit(alliance.getGuanduTwoLimit());
    response.setGuanduTwoArranged(warArrangementRepository.countByAllianceIdAndWarType(
        allianceId, WarType.GUANDU_TWO));
    response.setGuanduTwoPending(warApplicationRepository.countByAllianceIdAndWarTypeAndStatus(
        allianceId, WarType.GUANDU_TWO, WarApplication.ApplicationStatus.PENDING));
    response.setGuanduTwoTotal(response.getGuanduTwoArranged() + response.getGuanduTwoPending());
    response.setGuanduTwoFull(response.getGuanduTwoTotal() >= alliance.getGuanduTwoLimit());

    return response;
  }

  public WarArrangement moveGuanDuWar(Long accountId) {
    gameAccountRepository.findById(accountId).orElseThrow(() -> new BusinessException("账号不存在"));
    List<WarArrangement> arrangements =
        warArrangementRepository.findByAccountIdAndWarTypeIn(
            accountId, List.of(WarType.GUANDU_ONE, WarType.GUANDU_TWO));
    if (arrangements.size() != 1) {
      throw new BusinessException("账号不能同时参加官渡一和官渡二");
    }
    WarArrangement warArrangement = arrangements.get(0);
    if (warArrangement.getWarType().equals(WarType.GUANDU_ONE)) {
      warArrangement.setWarType(WarType.GUANDU_TWO);
    } else {
      warArrangement.setWarType(WarType.GUANDU_ONE);
    }
    warArrangement.setWarGroupId(null);
    warArrangementRepository.save(warArrangement);
    return warArrangement;
  }

  public WarArrangement addToWar(Long accountId, WarType warType) {
    GameAccount account =
        gameAccountRepository.findById(accountId).orElseThrow(() -> new BusinessException("账号不存在"));
    Alliance alliance =
        allianceRepository
            .findById(account.getAllianceId())
            .orElseThrow(() -> new BusinessException("账号所在的联盟不存在"));
    if (!UserContext.getUserId().equals(alliance.getLeaderId())) {
      throw new BusinessException("只有盟主才能添加成员到战事中");
    }
    if (warType == WarType.GUANDU_ONE || warType == WarType.GUANDU_TWO) {
      List<WarArrangement> arrangements =
          warArrangementRepository.findByAccountIdAndWarTypeIn(
              accountId, List.of(WarType.GUANDU_ONE, WarType.GUANDU_TWO));
      if (arrangements.size() == 1) {
        WarArrangement arrangement = arrangements.get(0);
        if (arrangement.getWarType().equals(warType)) {
          throw new BusinessException("当前成员已在当前战事中");
        } else {
          throw new BusinessException("当前成员不能同时参加官渡一和官渡二");
        }
      }
    }
    WarArrangement arrangement = new WarArrangement();
    arrangement.setAccountId(accountId);
    arrangement.setAllianceId(alliance.getId());
    arrangement.setWarType(warType);
    warArrangementRepository.save(arrangement);
    return arrangement;
  }

  @Transactional
  public void removeFromWar(Long accountId, WarType warType) {
    GameAccount account =
        gameAccountRepository
            .findById(accountId)
            .orElseThrow(() -> new BusinessException("当前账号不存在"));
    Alliance alliance =
        allianceRepository
            .findById(account.getAllianceId())
            .orElseThrow(() -> new BusinessException("账号所在联盟不存在"));
    if (!UserContext.getUserId().equals(alliance.getLeaderId())) {
      throw new BusinessException("只有联盟盟主才能移除战事中的成员");
    }
    warArrangementRepository.deleteByAccountIdAndWarType(accountId, warType);
    warApplicationRepository.deleteByAccountIdAndWarType(accountId, warType);
  }

  @Transactional
  public void cancelApplyForWar(@Valid WarRequest warRequest) {
    GameAccount account =
        gameAccountRepository
            .findById(warRequest.getAccountId())
            .orElseThrow(() -> new BusinessException("账号不存在"));
    if (!account.getUserId().equals(UserContext.getUserId())) {
      throw new BusinessException("不能取消他人的战事申请");
    }
    warApplicationRepository.deleteByAccountIdAndWarType(
        warRequest.getAccountId(), warRequest.getWarType());
  }

  @Transactional
  public void useTactic(Long allianceId, UseTacticRequest request) {
    // 验证所选的战术是否支持该战事类型
    WarType warType = request.getWarType();
    WarTactic tactic = request.getTactic();
    if (!tactic.getSupportedWarTypes().contains(warType)) {
      throw new BusinessException("该战术不支持当前战事类型");
    }
    Long userId = UserContext.getUserId();
    // 验证是否为盟主
    Alliance alliance =
        allianceRepository
            .findById(allianceId)
            .orElseThrow(() -> new BusinessException("联盟不存在"));
    if (!alliance.getLeaderId().equals(userId)) {
      throw new BusinessException("只有盟主可以安排战事");
    }
    // 删除联盟下的所有战事分组和战事安排
    List<WarArrangement> warArrangements =
        warArrangementRepository.findByAllianceIdAndWarTypeOrderByCreatedAtDesc(
            allianceId, warType);
    Set<Long> accountIds = new HashSet<>();
    if (!warArrangements.isEmpty()) {
      warArrangements.forEach(
          arrangement -> accountIds.add(arrangement.getAccountId()));
      warArrangementRepository.deleteAll(warArrangements);
    }
    warGroupRepository.deleteByAllianceIdAndWarType(allianceId, warType);
    if (accountIds.isEmpty()) {
      return;
    }
    List<GameAccount> accounts = gameAccountRepository.findAllById(accountIds);
    List<TacticalArrangement> arrangement = tactic.arrangement(accounts);
    arrangement.forEach(
        tacticalArrangement -> {
          List<WarArrangement> tacticalArrangements = tacticalArrangement
              .getWarArrangements();
          if (tacticalArrangements.isEmpty()) {
            return;
          }
          WarGroup warGroup = tacticalArrangement.getWarGroup();
          warGroup.setWarType(warType);
          warGroup.setAllianceId(allianceId);
          warGroupRepository.save(warGroup);
          tacticalArrangements.forEach(
              warArrangement -> {
                warArrangement.setWarType(warType);
                warArrangement.setAllianceId(allianceId);
                warArrangement.setWarGroupId(warGroup.getId());
              });
          warArrangementRepository.saveAll(tacticalArrangements);
        });
  }
}
