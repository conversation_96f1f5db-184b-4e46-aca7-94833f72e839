package com.app.gamehub.service;

import com.app.gamehub.dto.CreateGameAccountRequest;
import com.app.gamehub.dto.UpdateGameAccountRequest;
import com.app.gamehub.entity.Alliance;
import com.app.gamehub.entity.GameAccount;
import com.app.gamehub.exception.BusinessException;
import com.app.gamehub.repository.AllianceApplicationRepository;
import com.app.gamehub.repository.AllianceRepository;
import com.app.gamehub.repository.BarbarianGroupRepository;
import com.app.gamehub.repository.GameAccountRepository;
import com.app.gamehub.repository.PositionGrabRepository;
import com.app.gamehub.repository.UserRepository;
import com.app.gamehub.repository.WarApplicationRepository;
import com.app.gamehub.repository.WarArrangementRepository;
import com.app.gamehub.util.UserContext;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class GameAccountService {

  @Autowired private GameAccountRepository gameAccountRepository;

  @Autowired private UserRepository userRepository;

  @Autowired private AllianceRepository allianceRepository;

  @Autowired private PositionGrabRepository positionGrabRepository;

  @Autowired private WarApplicationRepository warApplicationRepository;

  @Autowired private WarArrangementRepository warArrangementRepository;

  @Autowired private AllianceApplicationRepository allianceApplicationRepository;

  @Autowired private BarbarianGroupRepository barbarianGroupRepository;

  @Transactional
  public GameAccount createGameAccount(CreateGameAccountRequest request) {
    Long userId = UserContext.getUserId();
    // 验证用户是否存在
    if (!userRepository.existsById(userId)) {
      throw new BusinessException("用户不存在");
    }

    // 检查用户在该区是否已有2个账号
    long accountCount =
        gameAccountRepository.countByUserIdAndServerId(userId, request.getServerId());
    if (accountCount >= 2) {
      throw new BusinessException("每个用户在每个区最多只能创建2个账号");
    }

    GameAccount account = new GameAccount();
    account.setUserId(userId);
    account.setServerId(request.getServerId());
    account.setAccountName(request.getAccountName());
    account.setPowerValue(request.getPowerValue());
    account.setDamageBonus(request.getDamageBonus());
    account.setTroopLevel(request.getTroopLevel());
    account.setRallyCapacity(request.getRallyCapacity());
    account.setLvbuStarLevel(request.getLvbuStarLevel());

    return gameAccountRepository.save(account);
  }

  @Transactional
  public GameAccount updateGameAccount(Long accountId, UpdateGameAccountRequest request) {
    Long userId = UserContext.getUserId();
    GameAccount account =
        gameAccountRepository
            .findById(accountId)
            .orElseThrow(() -> new BusinessException("游戏账号不存在"));

    // 验证权限：账号所有者或盟主可以更新
    boolean canUpdate = account.getUserId().equals(userId);
    if (!canUpdate && account.getAllianceId() != null) {
      Alliance alliance = allianceRepository.findById(account.getAllianceId()).orElse(null);
      canUpdate = alliance != null && alliance.getLeaderId().equals(userId);
    }

    if (!canUpdate) {
      throw new BusinessException("没有权限更新此账号");
    }

    // 更新字段
    if (request.getAccountName() != null && !request.getAccountName().trim().isEmpty()) {
      account.setAccountName(request.getAccountName().trim());
    }
    if (request.getPowerValue() != null) {
      account.setPowerValue(request.getPowerValue());
    }
    if (request.getDamageBonus() != null) {
      account.setDamageBonus(request.getDamageBonus());
    }
    if (request.getTroopLevel() != null) {
      account.setTroopLevel(request.getTroopLevel());
    }
    if (request.getRallyCapacity() != null) {
      account.setRallyCapacity(request.getRallyCapacity());
    }
    if (request.getLvbuStarLevel() != null) {
      account.setLvbuStarLevel(request.getLvbuStarLevel());
    }
    if (request.getMemberTier() != null) {
      account.setMemberTier(request.getMemberTier());
    }

    return gameAccountRepository.save(account);
  }

  @Transactional(rollbackFor = Exception.class)
  public void deleteGameAccount(Long accountId) {
    Long userId = UserContext.getUserId();
    GameAccount account =
        gameAccountRepository
            .findById(accountId)
            .orElseThrow(() -> new BusinessException("游戏账号不存在"));

    // 验证是否为账号所有者
    if (!account.getUserId().equals(userId)) {
      throw new BusinessException("只能删除自己的账号");
    }

    log.info("开始删除游戏账号 ID: {}, 名称: {}", accountId, account.getAccountName());

    // 1. 删除所有官职抢夺记录（必须先删除，因为它们引用了账号）
    log.info("删除官职抢夺记录");
    positionGrabRepository.deleteByAccountId(accountId);
    positionGrabRepository.flush();

    // 2. 删除所有战事申请记录
    log.info("删除战事申请记录");
    warApplicationRepository.deleteAllByAccountId(accountId);
    warApplicationRepository.flush();

    // 3. 删除所有战事安排记录
    log.info("删除战事安排记录");
    warArrangementRepository.deleteAllByAccountId(accountId);
    warArrangementRepository.flush();

    // 4. 删除所有联盟申请记录
    log.info("删除联盟申请记录");
    allianceApplicationRepository.deleteAllByAccountId(accountId);
    allianceApplicationRepository.flush();

    // 5. 处理南蛮分组关联（如果有的话）
    if (account.getBarbarianGroupId() != null) {
      Long groupId = account.getBarbarianGroupId();
      log.info("处理南蛮分组关联");

      // 先清空账号的分组关联
      account.setBarbarianGroupId(null);
      gameAccountRepository.save(account);
      gameAccountRepository.flush();

      // 检查分组是否还有其他成员，如果没有则删除分组
      long memberCount = barbarianGroupRepository.countMembersByGroupId(groupId);
      if (memberCount == 0) {
        barbarianGroupRepository.deleteById(groupId);
        log.info("南蛮分组 {} 因无成员而被自动删除", groupId);
      }
    }

    // 6. 清空账号的联盟和王朝关联（如果有的话）
    if (account.getAllianceId() != null || account.getDynastyId() != null) {
      log.info("清空账号的联盟和王朝关联");
      account.setAllianceId(null);
      account.setMemberTier(null);
      account.setDynastyId(null);
      gameAccountRepository.save(account);
      gameAccountRepository.flush();
    }

    // 7. 最后删除账号本身
    log.info("删除账号主体数据");
    gameAccountRepository.delete(account);

    log.info("游戏账号删除完成 ID: {}", accountId);
  }

  public List<GameAccount> getUserGameAccounts() {
    Long userId = UserContext.getUserId();
    return gameAccountRepository.findByUserIdOrderByServerIdDesc(userId);
  }

  public GameAccount getGameAccountById(Long accountId) {
    return gameAccountRepository
        .findById(accountId)
        .orElseThrow(() -> new BusinessException("游戏账号不存在"));
  }

  public List<GameAccount> getAllianceMembers(Long allianceId) {
    return gameAccountRepository.findByAllianceIdOrderByPowerValueDesc(allianceId);
  }
}
