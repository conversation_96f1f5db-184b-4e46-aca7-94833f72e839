# Database Configuration
spring.datasource.url=jdbc:mysql://${DB_HOST:127.0.0.1}:${DB_PORT:3306}/game_hub?useSSL=true&serverTimezone=Asia/Shanghai
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:KV753t0PpVmpjd2d}
# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.open-in-view=true
spring.jpa.properties.hibernate.jdbc.time_zone=Asia/Shanghai
# JWT Configuration
jwt.secret=3f5a7b2c9e1d4g6h8i0jklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890!@#$%^&*()_+
jwt.expiration=86400000
# WeChat Mini Program Configuration
wechat.miniapp.appid=${WX_APP_ID:wx2c74c887af0d1857}
wechat.miniapp.secret=${WX_SECRET:1138fba4df2b2de17d70cdc2d164b52b}
# Swagger Configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
# Server Configuration
server.port=8080
spring.main.banner-mode=off
logging.level.org.hibernate.orm.connections.pooling=warn
